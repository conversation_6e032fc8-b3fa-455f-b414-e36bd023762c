"use client";
import React from "react";

interface SettingsToggleProps {
  id: string;
  label: string;
  description?: string;
  checked: boolean;
  onChange: (checked: boolean) => void;
  error?: string;
  disabled?: boolean;
}

const SettingsToggle: React.FC<SettingsToggleProps> = ({
  id,
  label,
  description,
  checked,
  onChange,
  error,
  disabled = false,
}) => {
  return (
    <div className="flex items-center justify-between py-3">
      <div className="flex-1 mr-4">
        <label htmlFor={id} className="block text-lg text-white font-medium mb-2">
          {label}
        </label>
        {description && (
          <p className="text-sm text-[#A3A3A3] max-w-xs">{description}</p>
        )}
        {error && (
          <p className="text-red-500 text-xs mt-1 error-message">{error}</p>
        )}
      </div>
      
      <div className="flex-shrink-0">
        <button
          type="button"
          id={id}
          role="switch"
          aria-checked={checked}
          disabled={disabled}
          onClick={() => !disabled && onChange(!checked)}
          className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors focus:outline-none ${
            disabled
              ? "cursor-not-allowed opacity-50"
              : "cursor-pointer"
          } ${
            checked
              ? "bg-[#BFBFBF]"
              : "bg-[#3D3D3D]"
          }`}
        >
          <span
            className={`inline-block h-4.5 w-4.5 transform rounded-full transition-transform ${
              checked ? "translate-x-6 bg-[#2E2E2E]" : "translate-x-1 bg-white"
            }`}
          />
        </button>
      </div>
    </div>
  );
};

export default SettingsToggle;
